package com.xin.gleezy.and.plugins

import android.content.Context
import android.util.Log
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.Worker
import androidx.work.WorkerParameters
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import java.util.concurrent.TimeUnit

class BackgroundTaskWorker(context: Context, workerParams: WorkerParameters) :
    Worker(context, workerParams) {

    override fun doWork(): Result {
        Log.d("BackgroundTaskWorker", "执行后台任务")

        return Result.success()
    }
}

class BackgroundTaskPlugin : FlutterPlugin, MethodCallHandler {
    private var channel: MethodChannel? = null
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
        channel =
            MethodChannel(flutterPluginBinding.binaryMessenger, Channel.backgroundTask.channelName)
        channel?.setMethodCallHandler(this)
    }

    override fun onDetachedFromEngine(binding: FlutterPluginBinding) {
        channel?.setMethodCallHandler(null)
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        if (call.method == "startBackgroundTask") {
            startBackgroundTask()
            result.success(null)
        } else if (call.method == "endBackgroundTask") {
            endBackgroundTask()
            result.success(null)
        } else {
            result.notImplemented()
        }
    }

    private fun startBackgroundTask() {
        val workRequest = OneTimeWorkRequestBuilder<BackgroundTaskWorker>()
            .setInitialDelay(5, TimeUnit.SECONDS)
            .build()

        WorkManager.getInstance(context).enqueue(workRequest)
        Log.d("MainActivity", "后台任务已启动")
    }

    private fun endBackgroundTask() {
        Log.d("MainActivity", "App 进入前台，取消后台任务")
        WorkManager.getInstance(context).cancelAllWork()
    }
}
