package com.xin.gleezy.and.plugins

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import androidx.core.content.FileProvider
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import java.io.File


class InstallApkPlugin : FlutterPlugin, MethodCallHandler {
    private var channel: MethodChannel? = null
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
        channel =
            MethodChannel(flutterPluginBinding.binaryMessenger, Channel.installApk.channelName)
        channel?.setMethodCallHandler(this)
    }

    override fun onDetachedFromEngine(binding: FlutterPluginBinding) {
        channel?.setMethodCallHandler(null)
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        if (call.method == "install") {
            val filePath = call.argument<String>("apkPath") ?: ""
            if (!TextUtils.isEmpty(filePath)) {
                installApk(File(filePath), result);
            } else {
                result.error("installApk", "apkPath is null", null);
            }
        } else {
            result.notImplemented()
        }
    }

    private fun installApk(apkFile: File, result: MethodChannel.Result) {
        if (apkFile.exists() && apkFile.length() > 0) {
            val intent: Intent
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                //AUTHORITY NEEDS TO BE THE SAME ALSO IN MANIFEST
                val apkUri =
                    FileProvider.getUriForFile(
                        context,
                        context.packageName + "." + "ota_update_provider",
                        apkFile
                    )
                intent = Intent(Intent.ACTION_INSTALL_PACKAGE)
                intent.setData(apkUri)
                intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            } else {
                val fileUri = Uri.fromFile(apkFile);
                intent = Intent(Intent.ACTION_VIEW)
                intent.setDataAndType(fileUri, "application/vnd.android.package-archive")
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent);
            result.success(true)
        } else {
            result.success(false)
        }
    }
}
