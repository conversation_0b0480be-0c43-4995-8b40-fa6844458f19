package com.xin.gleezy.and

import com.xin.gleezy.and.plugins.BackgroundTaskPlugin
import com.xin.gleezy.and.plugins.InstallApkPlugin
import com.xin.gleezy.and.plugins.PermissionCheckPlugin
import io.flutter.embedding.android.FlutterActivity

class MainActivity : FlutterActivity() {
    private val TAG = "MainActivity"
    override fun configureFlutterEngine(flutterEngine: io.flutter.embedding.engine.FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        try {
            flutterEngine.plugins.add(BackgroundTaskPlugin())
            flutterEngine.plugins.add(InstallApkPlugin())
            flutterEngine.plugins.add(PermissionCheckPlugin())
        } catch (e: Exception) {
            io.flutter.Log.e(
                TAG,
                "Error registering plugin",
                e
            )
        }
    }

}
