import 'package:and/l10n/language.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/model/invite_code.dart';
import 'package:and/model/user_login_info.dart';
import 'package:flutter/foundation.dart';

import 'cache.dart';
import 'keys.dart';

abstract class CacheHelper {
  static bool get devTestMode =>
      Cache.instance.getBool(Keys.devTestMode, defValue: kDebugMode);

  static void saveDevTestMode(bool agree) =>
      Cache.instance.putBool(Keys.devTestMode, agree);

  static UserLoginInfo? get userProfile => Cache.instance
      .getObject(Keys.userInfo, (map) => UserLoginInfo.fromJson(map));

  static void saveUserProfile(UserLoginInfo account) =>
      Cache.instance.putObject(Keys.userInfo, account);

  static void clearUserProfile() =>
      Cache.instance.remove(Keys.userInfo);

  static bool get syncFriend => Cache.instance.getBool(Keys.syncFriend);

  static void saveSyncFriend(bool sync) =>
      Cache.instance.putBool(Keys.syncFriend, sync);

  // static int get syncFriendVersion {
  //   return Cache.instance.getInt("${uid}_friend_sync_version");
  // }
  //
  // static void saveSyncFriendVersion(int version) =>
  //     Cache.instance.putInt("${uid}_friend_sync_version", version);

  static String? get token => userProfile?.token;

  static String? get uid => userProfile?.uid;

  static String? get deviceToken => Cache.instance.getString(Keys.deviceToken);

  static void saveDeviceToken(String token) =>
      Cache.instance.putString(Keys.deviceToken, token);

  static String? get currentCountryCode =>
      Cache.instance.getString(Keys.currentCountryCode, defValue: Language.fsLan.countryCode);

  static void saveCurrentCountryCode(String code) =>
      Cache.instance.putString(Keys.currentCountryCode, code);

  static InviteInfo? get inviteInfo => Cache.instance
      .getObject(Keys.inviteInfo, (map) => InviteInfo.fromJson(map));

  static void saveInviteInfo(InviteInfo info) =>
      Cache.instance.putObject(Keys.inviteInfo, info);

  static void clearInviteInfo() =>
      Cache.instance.remove(Keys.inviteInfo);

  static int? get lastShowUpdateTime =>
      Cache.instance.getInt(Keys.lastShowUpdateTime);

  static void saveLastShowUpdateTime(int time) =>
      Cache.instance.putInt(Keys.lastShowUpdateTime, time);

  static String? get lastShowUpdateVersion =>
      Cache.instance.getString(Keys.lastShowUpdateVersion);

  static void saveLastShowUpdateVersion(String version) =>
      Cache.instance.putString(Keys.lastShowUpdateVersion, version);

  static bool get caringModel =>
      Cache.instance.getBool(Keys.caringModel);

  static void saveCaringModel(bool caringModel) =>
      Cache.instance.putBool(Keys.caringModel, caringModel);

  /// Remove all
  static void clearAll() {
    clearUserProfile();
    clearInviteInfo();
  }
}
