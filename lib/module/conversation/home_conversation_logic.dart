import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/group_setting_keys.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/constant/wk_cmd_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_ui_conversation_msg_ext.dart';
import 'package:and/module/chat/chat_logic.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class HomeConversationLogic extends ListController<WKUIConversationMsg> {
  final String key = "ui_conversation";
  var allUnreadCount = 0.obs;
  var nodeId = 0.obs;
  var status = Rx<int>(WKConnectStatus.connecting);
  var statusStr = Rx<String>("");

  HomeConversationLogic();

  @override
  void onReady() {
    super.onReady();
    _initListener();
    updateAllUnread();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<WKUIConversationMsg>?> loadData() async {
    var result = await WKIM.shared.conversationManager.getAll();
    await result.sortMsg();
    return result;
  }

  void updatePin(
    String channelID,
    int channelType,
    bool isPin,
  ) {
    if (channelType == WKChannelType.personal) {
      HttpUtils.updateUserSetting(channelID, "top", isPin ? 1 : 0);
    } else if (channelType == WKChannelType.group) {
      HttpUtils.updateGroupSetting(channelID, "top", isPin ? 1 : 0);
    }
  }

  void deleteMsg(
    BuildContext context,
    String channelID,
    int channelType,
  ) async {
    bool clearBoth = false;
    // 判断是否为群聊以及当前用户是否为群主或管理员
    bool isAdminOrManager = true;

    if (channelType == WKChannelType.group) {
      // 获取当前用户在该群的成员信息
      var member = await WKIM.shared.channelMemberManager
          .getMember(channelID, WKChannelType.group, CacheHelper.uid ?? '');
      isAdminOrManager = member?.role == WKChannelMemberRole.admin ||
          member?.role == WKChannelMemberRole.manager;
    }
    bool? confirm = await Get.dialog<bool>(
      AlertDialog(
        title: Text(context.l10n.deleteChat),
        content: isAdminOrManager
            ? StatefulBuilder(
                builder: (context, setState) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Checkbox(
                            value: clearBoth,
                            onChanged: (value) {
                              setState(() {
                                clearBoth = value ?? false;
                              });
                            },
                          ),
                          Expanded(
                            child: Text(context.l10n.clearHistoryBoth),
                          ),
                        ],
                      ),
                    ],
                  );
                },
              )
            : null,
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(context.l10n.globalDelete),
          ),
        ],
      ),
    );
    if (confirm == true) {
      await HttpUtils.clearHistory(channelID, channelType,
          clearBoth: clearBoth, isDeleteCov: true);
    }
  }

  Future updateMute(
    String channelID,
    int channelType,
    bool isMute,
  ) async {
    await EasyLoadingHelper.show(onAction: () async {
      if (channelType == WKChannelType.personal) {
        HttpUtils.updateUserSetting(
            channelID, GroupSettingKeys.mute, isMute ? 1 : 0);
      } else if (channelType == WKChannelType.group) {
        HttpUtils.updateGroupSetting(
            channelID, GroupSettingKeys.mute, isMute ? 1 : 0);
      }
    });
  }

  updateAllUnread() {
    WKIM.shared.conversationManager.getAllUnreadCount().then((value) {
      allUnreadCount.value = value;
    });
  }

  _initListener() {
    // 监听连接状态事件
    WKIM.shared.connectionManager.addOnConnectionStatus(key,
        (status, reason, connInfo) {
      this.status.value = status;
      if (connInfo != null) {
        nodeId.value = connInfo.nodeId;
      }
      if (status == WKConnectStatus.connecting) {
        statusStr.value =
            globalContext?.l10n.statusConnecting ?? 'Connecting...';
      } else if (status == WKConnectStatus.success) {
        statusStr.value =
            globalContext?.l10n.statusSuccess ?? '连接成功(节点:${connInfo?.nodeId})';
      } else if (status == WKConnectStatus.noNetwork) {
        statusStr.value = globalContext?.l10n.statusNoNetwork ?? '网络异常';
      } else if (status == WKConnectStatus.syncMsg) {
        statusStr.value = globalContext?.l10n.statusSyncMsg ?? '同步消息中...';
      } else if (status == WKConnectStatus.kicked) {
        statusStr.value = globalContext?.l10n.statusKicked ?? '未连接，在其他设备登录';
        CommonHelper.exitLogin();
      } else if (status == WKConnectStatus.fail) {
        statusStr.value = globalContext?.l10n.statusFail ?? '未连接';
      } else if (status == WKConnectStatus.syncCompleted) {
        statusStr.value =
            globalContext?.l10n.statusSyncCompleted ?? '连接成功(节点:$nodeId)';
      }
    });
    //监听清除所有未读红点
    WKIM.shared.conversationManager.addOnClearAllRedDotListener(key, () {
      list.refresh();
    });
    //监听最近会话列表的变化
    WKIM.shared.conversationManager.addOnRefreshMsgListListener(key,
        (msgs) async {
      if (msgs.isEmpty) {
        return;
      }
      // 使用 Map 来存储 msgList，以便快速查找和更新
      Map<String, WKUIConversationMsg> msgMap = {};
      for (var conversation in list) {
        msgMap[conversation.channelID] = conversation;
      }
      List<WKUIConversationMsg> newList = [];
      for (WKUIConversationMsg msg in msgs) {
        if (msgMap.containsKey(msg.channelID)) {
          // 更新现有消息
          msgMap[msg.channelID] = msg;
        } else {
          // 添加新消息
          newList.add(msg);
        }
      }

      // 更新 msgList
      var totalList = <WKUIConversationMsg>[];
      totalList.addAll(msgMap.values.toList());
      totalList.addAll(newList);
      await totalList.sortMsg();

      list.value = totalList;
    });
    //监听移除最近会话
    WKIM.shared.conversationManager.addOnDeleteMsgListener(key,
        (channelId, channelType) {
      var index = list.indexWhere((msg) =>
          msg.channelID == channelId && msg.channelType == channelType);
      if (index != -1) {
        list.removeAt(index);
      }
      updateAllUnread();
    });
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      var msg = list.firstWhereOrNull((msg) =>
          msg.channelID == channel.channelID &&
          msg.channelType == channel.channelType);
      if (msg != null) {
        msg.setWkChannel(channel);
      }
      list.sortMsg();
    });

    // 清除聊天记录
    WKIM.shared.messageManager.addOnClearChannelMsgListener(key,
        (channelID, channelType) {
      var index = list.indexWhere((msg) =>
          msg.channelID == channelID && msg.channelType == channelType);
      if (index != -1) {
        list.removeAt(index);
      }
      updateAllUnread();
    });

    // 消息更新接口
    WKIM.shared.messageManager.addOnRefreshMsgListener(key, (wkMsg) async {
      var msg = list.firstWhereOrNull((msg) =>
          msg.channelID == wkMsg.channelID &&
          msg.channelType == wkMsg.channelType &&
          msg.clientMsgNo == wkMsg.clientMsgNO);
      if (msg != null) {
        msg.setWkMsg(wkMsg);
      }
      list.sortMsg();
    });

    // @消息的监听
    WKIM.shared.reminderManager.addOnNewReminderListener(key, (reminders) {
      refreshData();
    });

    WKIM.shared.cmdManager.addOnCmdListener(key, (wkcmd) async {
      if (wkcmd.cmd == WKCMDKeys.syncChannelUnread) {
        refreshData();
        updateAllUnread();
      }
    });
  }

  _removeListener() {
    WKIM.shared.connectionManager.removeOnConnectionStatus(key);
    WKIM.shared.conversationManager.removeClearAllRedDotListener(key);
    WKIM.shared.conversationManager.removeOnRefreshMsgListListener(key);
    WKIM.shared.conversationManager.removeDeleteMsgListener(key);
    WKIM.shared.channelManager.removeOnRefreshListener(key);
    WKIM.shared.messageManager.removeClearChannelMsgListener(key);
    WKIM.shared.messageManager.removeOnRefreshMsgListener(key);
    WKIM.shared.reminderManager.removeOnNewReminderListener(key);
    WKIM.shared.cmdManager.removeCmdListener(key);
  }
}
