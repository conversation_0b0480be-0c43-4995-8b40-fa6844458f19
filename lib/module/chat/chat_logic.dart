import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/group_setting_keys.dart';
import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/constant/wk_cmd_keys.dart';
import 'package:and/constant/wk_group_type.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/conversation.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_call_content.dart';
import 'package:and/model/extension/sync_msg_ext.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/model/extension/wk_msg_reaction_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/model/pinned_status_message.dart';
import 'package:and/model/request/conversation_extra_request.dart';
import 'package:and/model/sticker_info.dart';
import 'package:and/model/voice_recognize.dart';
import 'package:and/module/chat/chat_send_utils.dart';
import 'package:and/module/chat/menu/message_menu_action.dart';
import 'package:and/module/chat/widget/input/panel/tool_panel_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_forward_preview_widget.dart';
import 'package:and/module/chat/widget/reaction_action_widget.dart';
import 'package:and/module/contact/choose/choose_contact_page.dart';
import 'package:and/module/group/group_detail_page.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/module/user/chat/chat_detail_page.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/module/videocall/videocall_argument.dart';
import 'package:and/module/videocall/videocall_logic.dart';
import 'package:and/utils/clipboard_utils.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_utils.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:and/widget/highlighted_text.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/proto/proto.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'controller/chat_msg_controller.dart';

enum ForwardType {
  /// 逐条
  item,

  /// 合并
  merge,
}

class PinnedMsg {
  PinnedStatusMessage pinnedMessage;
  WKMsg wkMsg;

  PinnedMsg({required this.pinnedMessage, required this.wkMsg});
}

class ChatLogic extends ChatMsgController {
  var channel = Rx<WKChannel?>(null);
  var channelMembers = RxList<WKChannelMember>();
  var currentMember = Rx<WKChannelMember?>(null);

  var pinnedMsg = Rx<PinnedMsg?>(null);
  var pinnedStatusMessage = RxList<PinnedStatusMessage>([]);

  /// 置顶消息对应的原始消息
  var pinnedOriginalMessages = RxList<WKMsg>([]);

  bool get isAnonymous {
    var role = currentMember.value?.role ?? WKChannelMemberRole.normal;
    return channel.value?.anonymous == 1 && role == WKChannelMemberRole.normal;
  }

  bool get isForbidden {
    var role = currentMember.value?.role ?? WKChannelMemberRole.normal;
    return channel.value?.forbidden == 1 && role == WKChannelMemberRole.normal;
  }

  var replyWKMsg = Rx<WKMsg?>(null);
  var isMultipleChoice = Rx<bool>(false);
  var choiceMsgs = RxList<String>();
  var recognizeMap = RxMap<String, VoiceRecognize>();
  var showToLastestBtn = false.obs;

  var readMsgIds = <String>{};
  var reminderDoneIds = <int>{}; //已读的reminderIds，待提交完成
  final _updateUnreadDebouncer = Debouncer(delay: Duration(milliseconds: 200));
  final _updateRemindDebouncer = Debouncer(delay: Duration(milliseconds: 200));
  final _updateReadedDebouncer = Debouncer(delay: Duration(milliseconds: 200));

  var isUpdateUnreadCount = false;
  var isUpdateReminder = false;
  var isUpdateReaded = false;

  bool get enableMentionAll => isGroupAdmin;

  bool get isGroupAdmin =>
      currentMember.value?.role == WKChannelMemberRole.admin ||
      currentMember.value?.role == WKChannelMemberRole.manager;

  late ChatSendUtils chatSendUtils = ChatSendUtils(
      channelType: channelType,
      channelID: channelID,
      onSendMsgStart: () {
        chatSendUtils.replyWKMsg = replyWKMsg.value;
      },
      onSendMsgEnd: () {
        //发送新消息，跳转到底部
        chatScrollController.jumpToBottom();

        replyWKMsg.value = null;

        //发新消息，清除未读消息
        unreadStartMsgOrderSeq = 0;
        if (unreadCount.value > 0) {
          clearUnread();
        }
      });

  ChatLogic({required super.argument}) {
    ever(channelMembers, (value) {
      currentMember.value = channelMembers
          .firstWhereOrNull((e) => e.memberUID == CacheHelper.uid);
    });
  }

  @override
  void onReady() async {
    await EasyLoadingHelper.show(
        delay: Duration(milliseconds: 300),
        onAction: () async {
          await _initData();
        });
    super.onReady();
    _initListener();
  }

  @override
  void onClose() {
    _finishTask();
    _removeListener();
    super.onClose();
  }

  _finishTask() async {
    _updateUnreadDebouncer.cancel();
    _updateRemindDebouncer.cancel();
    _updateReadedDebouncer.cancel();
    isUpdateUnreadCount = false;
    isUpdateReminder = false;
    isUpdateReaded = false;
    await updateUnreadCount();
    await updateReminderDone();
    await updateMsgReaded();
    pinnedMsg.value = null;
  }

  Future<void> _initData() async {
    var channel =
        await WKIM.shared.channelManager.getChannel(channelID, channelType);
    WKIM.shared.channelManager.fetchChannelInfo(channelID, channelType);
    this.channel.value = channel;

    // 获取置顶消息列表
    loadPinnedMessages();

    //同步扩展信息
    HttpUtils.syncMsgExtra(channelID, channelType);
    HttpUtils.syncReactions(channelID, channelType);
    //如果是群聊就同步群成员信息
    if (channelType == WKChannelType.group) {
      var groupType = channel?.remoteExtraMap[WKChannelExtras.groupType];
      if (groupType == WkGroupType.normalGroup) {
        channelMembers.value = (await WKIM.shared.channelMemberManager
                .getMembers(channelID, channelType)) ??
            [];
        HttpUtils.syncGroupMembers(channelID);
      }
    }
  }

  /// 加载置顶消息列表
  Future<void> loadPinnedMessages() async {
    try {
      final response =
          await HttpUtils.getPinnedMessages(channelID, channelType);

      pinnedOriginalMessages.value = (response?.messages ?? [])
          .map((e) => e.toWKSyncMsg().getWKMsg())
          .toList();
      pinnedStatusMessage.value = (response?.pinnedMessages ?? [])
          .where((msg) => msg.isDeleted != 1)
          .toList();

      pinnedStatusMessage.sort((a, b) => a.updatedAt.compareTo(b.updatedAt));
      PinnedStatusMessage? showPinnedStatusMessage =
          pinnedStatusMessage.isNotEmpty ? pinnedStatusMessage.last : null;
      if (showPinnedStatusMessage != null) {
        WKMsg? wkMsg = pinnedOriginalMessages.firstWhereOrNull(
            (msg) => msg.messageSeq == showPinnedStatusMessage.messageSeq);

        if (wkMsg != null) {
          pinnedMsg.value = PinnedMsg(
            pinnedMessage: showPinnedStatusMessage,
            wkMsg: wkMsg,
          );
        }
      } else {
        pinnedMsg.value = null;
      }
    } catch (e) {
      print('Failed to load pinned messages: $e');
    }
  }

  /// 加载置顶消息列表
  void clearPinnedMessage() async {
    pinnedStatusMessage.value = [];
    pinnedMsg.value = null;
    pinnedOriginalMessages.value = [];
  }

  /// 置顶消息
  /// 置顶消息
  Future<void> pinMessage(WKMsg msg) async {
    if (globalContext == null) {
      return;
    }
    try {
      // 如果是单聊，需要弹窗确认
      if (channelType == 1) {
        bool isChecked = false;
        bool? result = await Get.dialog<bool>(
          AlertDialog(
            title: Text(globalContext!.l10n.msgPinConfirm),
            content: StatefulBuilder(
              builder: (context, setState) {
                return Row(
                  children: [
                    Checkbox(
                      value: isChecked,
                      onChanged: (value) {
                        setState(() {
                          isChecked = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                        child: HighlightedText(
                      text: context.l10n.msgPinWithPeer(
                          msg.getChannelInfo()?.displayName ?? ""),
                      keyword: msg.getChannelInfo()?.displayName ?? "",
                      highlightColor: Theme.of(context).primaryColor,
                      textStyle: TextStyles.fontSize16Normal.copyWith(
                        fontSize: 17,
                      ),
                    )),
                  ],
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: Text(globalContext!.l10n.globalCancel),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                child: Text(globalContext!.l10n.pinned),
              ),
            ],
          ),
        );

        if (result == true) {
          await HttpUtils.pinMessage(
            channelID,
            channelType,
            msg.messageID,
            msg.messageSeq,
            type: isChecked ? 2 : 1,
          );
        }
      } else {
        // 如果不是单聊，直接置顶
        await HttpUtils.pinMessage(
          channelID,
          channelType,
          msg.messageID,
          msg.messageSeq,
        );
      }
    } catch (e) {
      print('Failed to pin message: $e');
      rethrow;
    }
  }

  /// 取消置顶消息
  /// type: 1仅删除自己 2删除全部人 删除消息只取消自己的置顶
  Future<void> unpinMessage(String messageID, int messageSeq,
      {bool isDeleteMsg = false}) async {
    int type = isDeleteMsg ? 1 : 2;
    if (channelType == 1 && pinnedStatusMessage.isNotEmpty && !isDeleteMsg) {
      final pinnedMsg = pinnedStatusMessage.last;
      type = pinnedMsg.createdBy == CacheHelper.uid ? 2 : 1;
    }
    try {
      await HttpUtils.unpinMessage(
          channelID, channelType, messageID, messageSeq, type);
    } catch (e) {
      print('Failed to unpin message: $e');
      rethrow;
    }
  }

  _initListener() {
    // 监听刷新频道
    WKIM.shared.channelManager.addOnRefreshListener(channelID, (channel) {
      print('刷新频道');
      if (channel.channelID == channelID &&
          channel.channelType == channelType) {
        this.channel.value = channel;
        messages.refresh();
      }
    });

    // 监听发送消息入库返回
    WKIM.shared.messageManager.addOnMsgInsertedListener((wkMsg) {
      print('收到新消息');

      if (wkMsg.channelID == channelID && wkMsg.channelType == channelType) {
        _receivedMessages([wkMsg], isReceive: false);
      }
    });

    // 监听新消息
    WKIM.shared.messageManager.addOnNewMsgListener(channelID, (msgs) {
      print('收到${msgs.length}条新消息');
      //如果当前消息还没加载，则忽略新消息，避免重复插入
      if (messages.isEmpty && isLoading) return;

      var currentMsgs = msgs.where((msg) {
        return (msg.channelID == channelID && msg.channelType == channelType);
      }).toList();

      _receivedMessages(currentMsgs);
    });

    // 监听消息刷新
    WKIM.shared.messageManager.addOnRefreshMsgListener(channelID, (wkMsg) {
      if (wkMsg.channelID == channelID && wkMsg.channelType == channelType) {
        if (wkMsg.wkMsgExtra?.isMutualDeleted == 1) {
          messageController
              .removeWhere((msg) => msg.clientMsgNO == wkMsg.clientMsgNO);
          return;
        }
        messageController.updateWhere(
            wkMsg, (msg) => msg.clientMsgNO == wkMsg.clientMsgNO);
      }
    });

    // 监听删除消息
    WKIM.shared.messageManager.addOnDeleteMsgListener(channelID, (clientMsgNo) {
      messageController.removeWhere((msg) => msg.clientMsgNO == clientMsgNo);
    });

    // 清除聊天记录
    WKIM.shared.messageManager.addOnClearChannelMsgListener(channelID,
        (channelId, channelType) {
      if (channelID == channelId && channelType == this.channelType) {
        messageController.set([]);
        clearPinnedMessage();
      }
    });

    // @消息的监听
    WKIM.shared.reminderManager.addOnNewReminderListener(channelID,
        (reminders) {
      resetReminder();
    });

    // 群成员的监听
    WKIM.shared.channelMemberManager.addOnNewMemberListener(channelID,
        (members) {
      var items = members.where((e) => e.channelID == channelID);
      if (items.isEmpty) {
        return;
      }
      channelMembers.addAll(items);
      messages.refresh();
    });
    WKIM.shared.channelMemberManager.addOnDeleteMemberListener(channelID,
        (members) {
      var memberIds = members.map((e) => e.memberUID).toList();
      if (memberIds.isEmpty) {
        return;
      }
      channelMembers.removeWhere((e) => memberIds.contains(e.memberUID));
      messages.refresh();
    });
    WKIM.shared.channelMemberManager.addOnRefreshMemberListener(channelID,
        (member, isEnd) async {
      if (member.channelID != channelID) {
        return;
      }
      if (isEnd) {
        channelMembers.value = (await WKIM.shared.channelMemberManager
                .getMembers(channelID, channelType)) ??
            [];
      }
      //重置消息的成员信息
      for (var msg in messages) {
        msg.clearMemberOfFrom();
      }
      messages.refresh();
    });
    WKIM.shared.cmdManager.addOnCmdListener(channelID, (wkcmd) async {
      final channelID = wkcmd.param['channel_id'];
      final channelType = wkcmd.param['channel_type'];
      if (channelID == null) {
        return;
      }
      if (channelID == this.channelID && channelType == this.channelType) {
        if (wkcmd.cmd == WKCMDKeys.wkPinnedMessage) {
          loadPinnedMessages();
        } else if (wkcmd.cmd == WKCMDKeys.conversationMessageBothClear) {
          clearPinnedMessage();
        }
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(channelID);
    WKIM.shared.messageManager.removeNewMsgListener(channelID);
    WKIM.shared.messageManager.removeNewMsgListener(channelID);
    WKIM.shared.messageManager.removeOnRefreshMsgListener(channelID);
    WKIM.shared.messageManager.removeDeleteMsgListener(channelID);
    WKIM.shared.messageManager.removeClearChannelMsgListener(channelID);
    WKIM.shared.channelMemberManager.removeNewMemberListener(channelID);
    WKIM.shared.channelMemberManager.removeDeleteMemberListener(channelID);
    WKIM.shared.channelMemberManager.removeRefreshMemberListener(channelID);
    WKIM.shared.channelManager.removeOnRefreshListener(channelID);
    WKIM.shared.reminderManager.removeOnNewReminderListener(channelID);
    WKIM.shared.cmdManager.removeCmdListener(channelID);
  }

  void _receivedMessages(List<WKMsg> list, {bool isReceive = true}) async {
    var isContinue = isMessageContinue.value;

    for (var msg in list) {
      // 命令消息和撤回消息不显示在聊天
      if (msg.contentType == WkMessageContentType.insideMsg ||
          msg.contentType == WkMessageContentTypeExt.withdrawSystemInfo ||
          msg.isDeleted == 1 ||
          msg.header.noPersist) {
        print("忽略消息");
        continue;
      }

      if (msg.channelID == channelID && msg.channelType == channelType) {
        var hasMsg = messages.any((item) {
          return msg.clientMsgNO == item.clientMsgNO;
        });
        if (hasMsg) {
          continue;
        }

        final double maxScrollExtent =
            chatScrollController.scrollController.position.pixels;
        var (firstIndex, lastIndext) =
            chatScrollController.listController.visibleRange ?? (-1, -1);

        // 提前计算可见区域的总高度
        double visibleHeight = 0;
        if (firstIndex > 0) {
          final context = getKey(firstIndex).currentContext;
          if (context != null) {
            final renderBox = context.findRenderObject() as RenderBox;
            visibleHeight = renderBox.size.height;
          }
        }

        if (isContinue) {
          // 如果消息是连续的，则直接插入
          messageController.insert(msg);
          if (msg.messageSeq > maxMsgSeq) {
            maxMsgSeq = msg.messageSeq;
          }
          if (isReceive) {
            unreadCount.value += 1;
            //保持滚动位置不变
            if ((firstIndex > 0 && visibleHeight > 0)) {
              chatScrollController.scrollController
                  .jumpTo(maxScrollExtent + visibleHeight);
            }
          } else {
            // 如果是自己发送的消息，则滚动到底部
            chatScrollController.jumpToBottom(delay: 0);
          }
        } else {
          if (isReceive) {
            unreadCount.value += 1;
          } else {
            // 如果消息不是连续的，则需要重新加载
            jumpToLatest();
          }
        }
      }
    }
  }

  void chooseTool(BuildContext context, ChatToolType type) async {
    switch (type) {
      case ChatToolType.gallery:
        chooseImage(context);
        break;
      case ChatToolType.takePhoto:
        takePhoto(context);
        break;
      case ChatToolType.file:
        chooseFile(context);
        break;
      case ChatToolType.card:
        sendCardMessage();
        break;
    }
  }

  void takePhoto(BuildContext context) async {
    var file = await ImageUtils.pickImage(context,
        imageSource: ImageSource.camera, compress: true);
    if (file == null) {
      return;
    }
    chatSendUtils.sendMediaMessage(file);
  }

  void chooseImage(BuildContext context) async {
    List<File> files = await ImageUtils.pickImages(context,
        maxAssets: 9, compress: true, requestType: RequestType.common);

    if (files.isEmpty) {
      return;
    }
    for (var file in files) {
      chatSendUtils.sendMediaMessage(file);
    }
  }

  Future<void> chooseFile(BuildContext context) async {
    FilePickerResult? result =
        await FilePicker.platform.pickFiles(allowMultiple: false);

    if (result != null) {
      List<File> files = result.paths.map((path) => File(path!)).toList();
      for (var file in files) {
        chatSendUtils.sendFileMessage(file);
      }
    }
  }

  /// 发送个人名片
  Future<void> sendCardMessage() async {
    var chooseUser =
        await ChooseContactPage.open(isMultiSelect: false) ?? <WKChannel>[];
    if (chooseUser.isEmpty) {
      return;
    }

    chatSendUtils.sendCardMessage(chooseUser.first);
  }

  void sendVoiceMessage(String path) async {
    var file = File(path);
    chatSendUtils.sendVoiceMessage(file);
  }

  /// 清空未读消息数
  void clearUnread() {
    HttpUtils.clearUnread(channelID, channelType);
  }

  @override
  Future<void> jumpToLatest() async {
    clearUnread();
    await super.jumpToLatest();
  }

  Future<WKChannel> getChatChannelInfo() async {
    WKChannel? channel =
        await WKIM.shared.channelManager.getChannel(channelID, channelType);
    channel ??= WKChannel(channelID, channelType);
    return channel;
  }

  void onMessageTaped(WKMsg msg) {
    if (WkMessageContentTypeExt.isCallMsg(msg.contentType)) {
      WkCallContent? content =
          WkCallContent().decodeJson(jsonDecode(msg.content)) as WkCallContent?;
      VideoCallLogic.inviteCall(VideoCallArgument(
        callType: (content?.callType == 1 ? CallType.audio : CallType.video),
        channel: channel.value!,
      ));
    }
  }

  void onMessageViewed(WKMsg msg) async {
    if (msg.viewed == 0 && msg.contentType == WkMessageContentType.text) {
      msg.viewed = 1;
    }

    if ((msg.wkMsgExtra?.readed ?? 0) == 0 &&
        msg.setting.receipt == 1 &&
        msg.fromUID != CacheHelper.uid) {
      readMsgIds.add(msg.messageID);
    }

    // 保存最新浏览到的位置
    if (msg.messageSeq > browseTo.value) {
      browseTo.value = msg.messageSeq;
    }

    if (msg.messageSeq > lastVisibleMsgSeq) {
      lastVisibleMsgSeq = msg.messageSeq;
    }

    if (lastVisibleMsgSeq != 0) {
      var lastVisibleMsgOrderSeq = await WKIM.shared.messageManager
          .getMessageOrderSeq(lastVisibleMsgSeq, channelID, channelType);
      if (lastVisibleMsgOrderSeq < unreadStartMsgOrderSeq) {
        lastVisibleMsgSeq = WKIM.shared.messageManager
            .getReliableMessageSeq(unreadStartMsgOrderSeq);
        lastVisibleMsgSeq = lastVisibleMsgSeq - 1;
      }
    }
    //更新未读数量
    if (unreadCount.value > 0) {
      //延迟清除未读消息数量，避免频繁调用
      if (lastVisibleMsgSeq != 0) {
        unreadCount.value = max(maxMsgSeq - lastVisibleMsgSeq, 0);
      }
      _updateUnreadDebouncer.call(() async {
        await updateUnreadCount();
      });
    }

    //更新已读reminderIds
    var reminder =
        reminders.firstWhereOrNull((e) => e.messageID == msg.messageID);
    if (reminder != null) {
      reminderDoneIds.add(reminder.reminderID);
      reminders.remove(reminder);
    }
    var groupApprove =
        groupApproves.firstWhereOrNull((e) => e.messageID == msg.messageID);
    if (groupApprove != null) {
      reminderDoneIds.add(groupApprove.reminderID);
      groupApproves.remove(groupApprove);
    }
    _updateRemindDebouncer.call(() async {
      await updateReminderDone();
    });

    _updateReadedDebouncer.call(() async {
      await updateMsgReaded();
    });
  }

  Future<void> updateUnreadCount() async {
    if (isUpdateUnreadCount) {
      return;
    }
    isUpdateUnreadCount = true;
    await HttpUtils.clearUnread(channelID, channelType,
        unread: unreadCount.value);
    isUpdateUnreadCount = false;
  }

  Future<void> updateReminderDone() async {
    if (isUpdateReminder || reminderDoneIds.isEmpty) {
      return;
    }
    isUpdateReminder = true;
    var result = await HttpUtils.doneReminder(reminderDoneIds.toList());
    if (result) {
      reminderDoneIds.clear();
    }
    isUpdateReminder = false;
  }

  Future<void> updateMsgReaded() async {
    if (isUpdateReaded || readMsgIds.isEmpty) {
      return;
    }
    isUpdateReaded = true;
    var result =
        await HttpUtils.doneReaded(channelID, channelType, readMsgIds.toList());
    if (result) {
      readMsgIds.clear();
    }
    isUpdateReaded = false;
  }

  void openDetail() async {
    if (channelType == WKChannelType.customerService) {
      //TODO nothing
    } else if (channelType == WKChannelType.personal) {
      ChatDetailPage.open(channelId: channelID);
    } else {
      var member = await WKIM.shared.channelMemberManager
          .getMember(channelID, channelType, CacheHelper.uid ?? '');
      if (member == null || member.isDeleted == 1) {
        return;
      }
      GroupDetailPage.open(groupNo: channel.value?.channelID ?? '');
    }
  }

  void updateCoverExtra(String draft, {int keepMessageSeq = 0}) async {
    WKConversationMsgExtra extra = WKConversationMsgExtra();

    extra.draft = draft;
    extra.keepOffsetY = keepOffsetY;
    extra.keepMessageSeq = keepMessageSeq;
    extra.channelID = channelID;
    extra.channelType = channelType;
    extra.browseTo = browseTo.value;
    if (draft.isNotEmpty) {
      extra.draftUpdatedAt =
          (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
    }
    //保存本地
    WKIM.shared.conversationManager.updateMsgExtra(extra);
    //同步远程
    try {
      ConversationApi(MyHttp.dio).updateCoverExtra(
          channelID,
          channelType,
          ConversationExtraRequest(
              browseTo: browseTo.value,
              keepMessageSeq: keepMessageSeq,
              keepOffsetY: keepOffsetY,
              draft: draft));
    } catch (e) {
      print('updateCoverExtra error: $e');
    }
  }

  void onShowMenu(BuildContext context, WKMsg msg, GlobalKey key,
      LongPressStartDetails details) async {
    var items = (await MessageMenuActionRegistry().getMenus(msg))
        .map((e) => e.render(context, msg, (type) {
              switch (type) {
                case MessageMenuType.copy:
                  onCopy(msg);
                  break;
                case MessageMenuType.revoke:
                  onRevoke(msg);
                  break;
                case MessageMenuType.forward:
                  onForward([msg]);
                  break;
                case MessageMenuType.recognize:
                  onRecognize(msg);
                  break;
                case MessageMenuType.reply:
                  onReply(msg);
                  break;
                case MessageMenuType.delete:
                  onDeleteMessages([msg]);
                  break;
                case MessageMenuType.multipleChoice:
                  onMultipleChoice(msg);
                  break;
                case MessageMenuType.pin:
                  if (isPinnedMessage(msg)) {
                    unpinMessage(msg.messageID, msg.messageSeq);
                  } else {
                    pinMessage(msg);
                  }
                  break;
                case MessageMenuType.sticker:
                  onAddSticker(msg);
                  break;
              }
            }, MessageMenuCallback(isPinnedMessage: isPinnedMessage)))
        .toList();

    Widget? reactionWidget;
    if (msg.isNormalMsg && !VideoCallLogic.isCallMsg(msg)) {
      reactionWidget = ReactionActionWidget(
        msg: msg,
        onEmojiTap: (emoji) {
          onEmoji(msg, emoji);
        },
      );
    }
    await PopMenuUtil.showCustomPopupMenu(context, key, items,
        headerView: reactionWidget);
  }

  void onEmoji(WKMsg msg, String emoji) async {
    var result = await HttpUtils.reactionEmoji(
        channelID, channelType, msg.messageID, emoji);
    if (result) {
      await addEmoji(msg, emoji);
    }
  }

  Future<WKMsgReaction> addEmoji(WKMsg msg, String emoji) async {
    var reactionList = (msg.reactionList ?? []);
    var oldReaction = reactionList.firstWhereOrNull(
        (element) => element.emoji == emoji && element.uid == CacheHelper.uid);
    WKMsgReaction? reaction;
    if (oldReaction != null) {
      print("update reaction");
      oldReaction.isDeleted = 1;
      reaction = oldReaction;
    } else {
      print("add reaction");
      var seq = await WKIM.shared.messageManager
          .getMaxReactionSeq(msg.channelID, msg.channelType);

      reaction = WKMsgReaction();
      reaction.channelID = msg.channelID;
      reaction.channelType = msg.channelType;
      reaction.uid = CacheHelper.uid ?? '';
      var userName = CacheHelper.userProfile?.name ?? '';
      if (channelType == WKChannelType.group) {
        userName = currentMember.value?.displayName ?? userName;
      }
      reaction.name = userName;
      reaction.emoji = emoji;
      reaction.seq = seq + 1;
      reaction.isDeleted = 0;
      reaction.messageID = msg.messageID;
      reaction.createdAt =
          DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    }

    WKIM.shared.messageManager
        .saveMessageReactions([reaction.toWKSyncMsgReaction()]);

    return reaction;
  }

  void onAddSticker(WKMsg msg) {
    var messageContent = msg.messageContent as WKImageContent?;
    if (messageContent != null) {
      EasyLoadingHelper.show(onAction: () async {
        await HttpUtils.addSticker(
            messageContent.url, messageContent.width, messageContent.height);
      });
    }
  }

  void onPickEmoji(BuildContext context, WKMsg msg, GlobalKey key) {
    Widget reactionWidget = ReactionActionWidget(
      msg: msg,
      onEmojiTap: (emoji) {
        onEmoji(msg, emoji);
      },
    );
    PopMenuUtil.showCustomPopupMenu(context, key, [],
        headerView: reactionWidget);
  }

  void onCopy(WKMsg msg) {
    var content = msg.messageContent?.content ?? '';
    ClipboardUtils.copyToClipboard(content);
  }

  void onRevoke(WKMsg msg) {
    EasyLoadingHelper.show(onAction: () async {
      if (isPinnedMessage(msg)) {
        await unpinMessage(msg.messageID, msg.messageSeq);
      }
      await HttpUtils.revokeMsg(
          msg.clientMsgNO, msg.messageID, msg.channelID, msg.channelType);
    });
  }

  void onMultipleChoice(WKMsg msg) {
    isMultipleChoice.value = true;
    choiceMsgs.add(msg.clientMsgNO);
    messages.refresh();
  }

  Future<void> onForward(List<WKMsg> msgList,
      {ForwardType type = ForwardType.item}) async {
    if (msgList.isEmpty) return;

    if (isAnonymous) {
      showToast(globalContext?.l10n.anonymousForbidForward ?? '');
      return;
    }

    await GlobalSearchPage.open(
        isMultiSelect: true,
        searchType: GlobalSearchType.recentConversation,
        title: globalContext?.l10n.chooseContact,
        onSelectedChannel: (channels) async {
          var context = globalContext;
          if (context == null) return;

          var content = msgList.first.messageContent?.displayText() ?? '';
          if (type == ForwardType.item) {
            if (msgList.length > 1) {
              content = context.l10n.itemForwardCount(msgList.length);
            }
          } else {
            content = context.l10n.lastMsgChatRecord;
          }
          var result = (await DialogUtils.showCustomDialog(context,
                  title: context.l10n.sendTo,
                  content: Container(
                      constraints: BoxConstraints(maxHeight: 300),
                      child: MsgForwardPreviewWidget(
                          channels: channels, content: content)))) ??
              false;

          if (!result) return;

          if (type == ForwardType.item) {
            //逐条转发
            for (var channel in channels) {
              for (var msg in msgList) {
                forwardMsg(msg, channel);
              }
            }
          } else if (type == ForwardType.merge) {
            //合并转发
            var mergeMsg = await msgList.toMergeMsg();
            for (var channel in channels) {
              Setting setting = Setting();
              setting.receipt = channel.receipt; //开启回执

              chatSendUtils.sendMsg(mergeMsg,
                  toChannel: channel, msgSetting: setting);
            }
          }
          showToast(context.l10n.forwardSuccess);

          Get.back();

          cancelMultipleChoice();
        });
  }

  Future<void> forwardMsg(WKMsg msg, WKChannel channel) async {
    chatSendUtils.forwardMsg(msg, channel);
  }

  void onReply(WKMsg msg) async {
    var isForbidden = channel.value?.forbidden == 1;
    if (isForbidden &&
        currentMember.value?.role == WKChannelMemberRole.normal) {
      var context = globalContext;
      if (context != null) {
        DialogUtils.showAlertDialog(context, context.l10n.cannotReplyMsg);
      }
      return;
    }
    replyWKMsg.value = msg;
  }

  void clearReply() {
    replyWKMsg.value = null;
  }

  void onDeleteMsg(WKMsg msg) async {
    onDeleteMessages([msg]);
  }

  void onSelectedChange(WKMsg item, bool isChoice) {
    if (isChoice) {
      if (choiceMsgs.length >= 100) {
        var context = globalContext;
        if (context != null) {
          showToast(context.l10n.maxChooseMsgCount(100));
        }
        return;
      }
      if (!choiceMsgs.contains(item.clientMsgNO)) {
        choiceMsgs.add(item.clientMsgNO);
      }
    } else {
      choiceMsgs.remove(item.clientMsgNO);
    }
    messages.refresh();
  }

  void cancelMultipleChoice() {
    isMultipleChoice.value = false;
    choiceMsgs.clear();
    messages.refresh();
  }

  void choiceForward() {
    var context = globalContext!;
    DialogUtils.showSelectBottomActionSheet(context, children: [
      CupertinoActionSheetAction(
        onPressed: () async {
          _forwardMessageAction(context, ForwardType.item);
        },
        child:
            Text(context.l10n.itemForward, style: TextStyles.fontSize16Normal),
      ),
      CupertinoActionSheetAction(
        onPressed: () async {
          _forwardMessageAction(context, ForwardType.merge);
        },
        child:
            Text(context.l10n.mergeForward, style: TextStyles.fontSize16Normal),
      )
    ]);
  }

  void _forwardMessageAction(BuildContext context, ForwardType type) async {
    var msgList = messages
        .where((element) => choiceMsgs.contains(element.clientMsgNO))
        .toList();
    await onForward(msgList, type: type);
    Get.back();
  }

  void choiceDelete() async {
    var msgList = messages
        .where((element) => choiceMsgs.contains(element.clientMsgNO))
        .toList();
    onDeleteMessages(msgList);
  }

  void onDeleteMessages(List<WKMsg> msgList) async {
    var context = globalContext!;
    bool clearBoth = false;
    bool? result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(context.l10n.deleteMessages),
        content: StatefulBuilder(
          builder: (context, setState) {
            // 判断是否为群聊以及当前用户是否为群主或管理员
            bool isGroup = channel.value?.channelType == WKChannelType.group;
            bool isAdminOrManager = true;
            if (isGroup) {
              isAdminOrManager =
                  currentMember.value?.role == WKChannelMemberRole.admin ||
                      currentMember.value?.role == WKChannelMemberRole.manager;
            }

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  context.l10n.deleteSelectMsgTip,
                ),
                if (isAdminOrManager)
                  Row(
                    children: [
                      Checkbox(
                        value: clearBoth,
                        onChanged: (value) {
                          setState(() {
                            clearBoth = value ?? false;
                          });
                        },
                      ),
                      Expanded(
                        child: Text(context.l10n.clearHistoryBoth),
                      ),
                    ],
                  ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(context.l10n.globalConfirm),
          ),
        ],
      ),
    );
    if (result == true) {
      await EasyLoadingHelper.show(onAction: () async {
        var otherMsgList = msgList
            .where((e) => !e.isNormalMsg)
            .map((e) => e.clientMsgNO)
            .toList();
        if (otherMsgList.isNotEmpty) {
          await WKIM.shared.messageManager.deleteWithClientMsgNos(otherMsgList);
        }

        var normalMsgList = msgList.where((e) => e.isNormalMsg).toList();
        if (normalMsgList.isNotEmpty) {
          await HttpUtils.deleteMsg(normalMsgList,
              channelID: channelID, channelType: channelType, both: clearBoth);

          //如果只是删除本地消息，检查是否有pin的消息，并取消置顶
          if (!clearBoth) {
            for (var element in normalMsgList) {
              unpinMessage(element.messageID, element.messageSeq,
                  isDeleteMsg: true);
            }
          }
        }
      });
      cancelMultipleChoice();
    }
  }

  void sendSticker(StickerInfo sticker) async {
    chatSendUtils.sendStickerMessage(sticker);
  }

  void sendTextMessage(String content, List<String> mentionIds) async {
    chatSendUtils.sendTextMessage(content, mentionIds);
  }

  void resendMessage(WKMsg msg) async {
    chatSendUtils.resendMessage(msg);
  }

  void reEditMessage(WKMsg msg) {
    draft.value = msg.messageContent?.content ?? '';
    draft.refresh();
  }

  void onRecognize(WKMsg msg) {
    HttpUtils.voiceRecognize(msg, (result) {
      if (result == null) {
        recognizeMap.remove(msg.clientMsgNO);
      } else {
        recognizeMap[msg.clientMsgNO] = result;
      }
      messages.refresh();
    });
  }

  VoiceRecognize? getVoiceRecognize(WKMsg msg) {
    return recognizeMap[msg.clientMsgNO];
  }

  bool isPinnedMessage(WKMsg msg) {
    return msg.wkMsgExtra?.isPinned == 1 ||
        pinnedStatusMessage
            .any((element) => element.messageId == msg.messageID);
  }

  /// 聊天页根据orderSeq定位到指定消息
  void locationMsg(int orderSeq) {
    var index = messages.indexWhere((element) => element.orderSeq == orderSeq);
    if (index != -1) {
      chatScrollController.jumpToPosition(index, alignment: 0.5);
      return;
    }
    tipsOrderSeq = orderSeq;
    startRefresh(delay: Duration(milliseconds: 500));
  }

  Future updateMute(
    bool isMute,
  ) async {
    await EasyLoadingHelper.show(onAction: () async {
      if (channelType == WKChannelType.personal) {
        HttpUtils.updateUserSetting(
            channelID, GroupSettingKeys.mute, isMute ? 1 : 0);
      } else if (channelType == WKChannelType.group) {
        HttpUtils.updateGroupSetting(
            channelID, GroupSettingKeys.mute, isMute ? 1 : 0);
      }
    });
  }

  void viewUserInfo(String channelId) {
    String? groupNo;
    if (argument.channelType == WKChannelType.group && isGroupAdmin) {
      groupNo = argument.channelID;
    }
    UserInfoPage.open(channelID: channelId, groupNo: groupNo);
  }
}
