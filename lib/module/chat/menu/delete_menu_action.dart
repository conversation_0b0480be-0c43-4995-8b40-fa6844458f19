import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/module/chat/menu/message_menu_action.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class DeleteMenuAction extends MessageMenuAction {
  @override
  MenuItem render(
      BuildContext context, WKMsg msg, Function(MessageMenuType) onTap, MessageMenuCallback callback) {
    return MenuItem(
        text: context.l10n.delete,
        icon: ImagePath.ic_msg_delete,
        onTap: () {
          onTap(getType());
        });
  }

  @override
  Future<bool> canRender(WKMsg msg) async {
    if (msg.isSystemMsg) {
      return false;
    }

    var isShowRevoke = await msg.canRevoke();
    return !isShowRevoke;
  }

  @override
  MessageMenuType getType() {
    return MessageMenuType.delete;
  }
}
