import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/menu/message_menu_action.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class StickerMenuAction extends MessageMenuAction {
  @override
  MenuItem render(
      BuildContext context, WKMsg msg, Function(MessageMenuType) onTap, MessageMenuCallback callback) {
    return MenuItem(
        text: context.l10n.addSticker,
        icon: ImagePath.ic_add_emoji,
        onTap: () {
          onTap(getType());
        });
  }

  @override
  Future<bool> canRender(WKMsg msg) async {
    return msg.contentType == WkMessageContentTypeExt.sticker ||
        msg.contentType == WkMessageContentType.image ||
        msg.contentType == WkMessageContentType.gif;
  }

  @override
  MessageMenuType getType() {
    return MessageMenuType.sticker;
  }
}
