import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/module/chat/chat_logic.dart';
import 'package:and/module/chat/menu/message_menu_action.dart';
import 'package:and/module/videocall/videocall_logic.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class PinMenuAction extends MessageMenuAction {
  @override
  MenuItem render(
      BuildContext context, WKMsg msg, Function(MessageMenuType) onTap, MessageMenuCallback callback) {
    return MenuItem(
        text: callback.isPinnedMessage(msg)
            ? context.l10n.msgUnpinned
            : context.l10n.pinned,
        icon: ImagePath.ic_msg_top,
        onTap: () {
          onTap(getType());
        });
  }

  @override
  Future<bool> canRender(WKMsg msg) async {
    return msg.isNormalMsg && !VideoCallLogic.isCallMsg(msg);
  }

  @override
  MessageMenuType getType() {
    return MessageMenuType.pin;
  }
}
