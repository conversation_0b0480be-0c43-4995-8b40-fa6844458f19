import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';

class PromptPinnedTimeWidget extends StatelessWidget {
  final int timestamp;

  const PromptPinnedTimeWidget({super.key, required this.timestamp});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border(
          bottom: BorderSide(
            color: DColor.divider.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Center(
        child: Text(
          TimeUtils.formatTimestampToDay(timestamp),
          style: TextStyles.fontSize15Normal.copyWith(
            fontWeight: FontWeight.w500,
            color: DColor.primaryTextColor,
          ),
        ),
      ),
    );
  }
}
