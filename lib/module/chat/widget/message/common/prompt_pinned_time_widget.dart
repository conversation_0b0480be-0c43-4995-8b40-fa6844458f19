import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';

class PromptPinnedTimeWidget extends StatelessWidget {
  final int timestamp;

  const PromptPinnedTimeWidget({super.key, required this.timestamp});

  @override
  Widget build(BuildContext context) {
    return Text(
      TimeUtils.formatTimestampToDay(timestamp),
      style: TextStyles.fontSize15Normal,
    );
  }
}
