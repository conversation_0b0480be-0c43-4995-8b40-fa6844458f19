import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';

class PromptGroupTimeWidget extends StatelessWidget {
  final int timestamp;

  const PromptGroupTimeWidget({super.key, required this.timestamp});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0x1A000000),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            TimeUtils.getNewChatTime(timestamp),
            style: TextStyles.fontSize12Normal.copyWith(
              color: const Color(0x99000000),
            ),
          ),
        ),
      ),
    );
  }
}
