import 'package:and/app.dart';
import 'package:and/l10n/l10n.dart';
import 'package:intl/intl.dart';

class TimeUtils {
  static String formatDateTime(int timestamp) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return "${dateTime.year}-${dateTime.month}-${dateTime.day} ${dateTime.hour}:${dateTime.minute}:${dateTime.second}";
  }

  static String formatTimestampToMMDD(int timestamp) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return DateFormat('MM-dd').format(dateTime);
  }

  static bool isSameDayByTimestamp(int timestamp1, int timestamp2) {
    return isSameDay(DateTime.fromMillisecondsSinceEpoch(timestamp1 * 1000),
        DateTime.fromMillisecondsSinceEpoch(timestamp2 * 1000));
  }

  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  static String formatHHMM(int timeStamp) {
    DateTime targetTime = DateTime.fromMillisecondsSinceEpoch(timeStamp * 1000);
    return DateFormat('HH:mm').format(targetTime);
  }

  static String getNewChatTime(int timeStamp, {bool isShowHM = false}) {
    DateTime targetTime = DateTime.fromMillisecondsSinceEpoch(timeStamp * 1000);
    DateTime now = DateTime.now();

    // If same day, show HH:mm
    if (isSameDay(targetTime, now)) {
      return DateFormat('HH:mm').format(targetTime);
    }

    // If yesterday
    DateTime yesterday = now.subtract(const Duration(days: 1));
    if (isSameDay(targetTime, yesterday)) {
      var yesterdayStr = globalContext?.l10n.dateTimeYesterday ?? "昨天";
      return DateFormat(isShowHM ? '$yesterdayStr HH:mm' : 'HH:mm')
          .format(targetTime);
    }

    // If same year but not yesterday
    if (targetTime.year == now.year) {
      return DateFormat(isShowHM ? 'MM-dd HH:mm' : 'MM-dd').format(targetTime);
    }

    // If different year
    return DateFormat(isShowHM ? 'yyyy-MM-dd HH:mm' : 'yyyy-MM-dd')
        .format(targetTime);
  }

  static String formatDuration(int timeTrad, {String defaultValue = '00:00'}) {
    String mStr, sStr;
    if (timeTrad >= 60) {
      int m = timeTrad ~/ 60; // 使用 ~/ 进行整数除法
      int s = timeTrad % 60;
      mStr = m.toString();
      sStr = s.toString();
      if (m < 10) {
        mStr = "0$m";
      }
      if (s < 10) {
        sStr = "0$s";
      }
    } else {
      mStr = "00";
      sStr = timeTrad < 10 ? "0$timeTrad" : timeTrad.toString();
    }
    String showTime = '$mStr:$sStr';
    if (showTime == "00:00") {
      return defaultValue;
    }
    return showTime;
  }

  /// 判断两个时间戳是否在同一个时间组内（10分钟内）
  static bool isWithinTimeGroup(int timestamp1, int timestamp2,
      {int groupMinutes = 5}) {
    int timeDiff = (timestamp1 - timestamp2).abs();
    return timeDiff <= (groupMinutes * 60); // 转换为秒
  }
}
