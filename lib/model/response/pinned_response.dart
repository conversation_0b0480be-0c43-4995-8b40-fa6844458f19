
import 'package:and/model/pinned_status_message.dart';
import 'package:and/model/sync_msg.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pinned_response.g.dart';

@JsonSerializable()
class PinnedResponse {
  @Json<PERSON>ey(name: 'pinned_messages', defaultValue: [])
  final List<PinnedStatusMessage> pinnedMessages;
  @JsonKey(name: 'messages', defaultValue: [])
  final List<SyncMsg> messages;

  const PinnedResponse(
      this.pinnedMessages, this.messages);

  factory PinnedResponse.fromJson(Map<String, dynamic> json) =>
      _$PinnedResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PinnedResponseToJson(this);
}