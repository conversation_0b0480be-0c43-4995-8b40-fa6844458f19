
import 'package:and/app.dart';
import 'package:and/cache/cache.dart';
import 'package:and/module/videocall/videocall_logic.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/im/background_task.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.

  print("Handling a background message: ${message.messageId}");
}

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  // FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);// 保持原生启动页
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  await initApp();
  await initSDK();

  runApp(App());

  setSystemUI();
}

Future<void> initApp() async {
  await Cache.instance.init();

  await CommonHelper.init();
  await FileUtils.init();
}

void setSystemUI() async {
  SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark);
  SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

Future<void> initSDK() async {
  BackgroundTask.init();
  await NotificationUtils.init();
}
